@echo off
echo 设置Java和Gradle环境变量
echo ========================

echo 正在设置系统环境变量...

REM 设置JAVA_HOME
setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot" /M
if %errorlevel% neq 0 (
    echo 设置JAVA_HOME失败，尝试用户级别设置...
    setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
)

REM 设置GRADLE_HOME
setx GRADLE_HOME "D:\gradle-8.3" /M
if %errorlevel% neq 0 (
    echo 设置GRADLE_HOME失败，尝试用户级别设置...
    setx GRADLE_HOME "D:\gradle-8.3"
)

REM 获取当前PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SYSTEM_PATH=%%b"

REM 检查PATH中是否已包含Java和Gradle路径
echo %SYSTEM_PATH% | findstr /C:"%JAVA_HOME%\bin" >nul
if %errorlevel% neq 0 (
    echo 添加Java到PATH...
    setx PATH "%SYSTEM_PATH%;%JAVA_HOME%\bin;%GRADLE_HOME%\bin" /M
    if %errorlevel% neq 0 (
        echo 系统级别设置失败，尝试用户级别...
        setx PATH "%PATH%;%JAVA_HOME%\bin;%GRADLE_HOME%\bin"
    )
) else (
    echo Java路径已在PATH中
)

echo.
echo 环境变量设置完成！
echo JAVA_HOME: C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
echo GRADLE_HOME: D:\gradle-8.3
echo.
echo 请重新打开命令提示符或重启系统以使环境变量生效。
echo 之后可以直接使用 java 和 gradle 命令。
echo.
pause
