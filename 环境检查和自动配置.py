#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电力线路设备缺陷识别系统 - 环境检查和自动配置脚本
支持 Windows 和 Linux 系统
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import tarfile
import shutil
import json
from pathlib import Path

class EnvironmentChecker:
    def __init__(self):
        self.system = platform.system().lower()
        self.is_windows = self.system == 'windows'
        self.is_linux = self.system == 'linux'
        self.project_root = Path(__file__).parent
        self.temp_dir = self.project_root / 'temp_downloads'
        self.temp_dir.mkdir(exist_ok=True)
        self.mysql_path = self.get_mysql_path()

    def get_mysql_path(self):
        """获取MySQL可执行文件路径"""
        if self.is_windows:
            # 常见的MySQL安装路径
            possible_paths = [
                r'"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe"',
                r'"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysql.exe"',
                r'"C:\Program Files (x86)\MySQL\MySQL Server 8.0\bin\mysql.exe"',
                r'"C:\MySQL\bin\mysql.exe"',
                'mysql'  # 如果在PATH中
            ]
        else:
            possible_paths = ['mysql']

        for path in possible_paths:
            success, _, _ = self.run_command(f'{path} --version')
            if success:
                return path
        return 'mysql'  # 默认值
        
    def run_command(self, command, shell=True, capture_output=True, timeout=300):
        """执行系统命令"""
        try:
            result = subprocess.run(command, shell=shell, capture_output=capture_output,
                                  text=True, timeout=timeout)
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            print(f"命令执行超时: {command}")
            return False, "", "超时"
        except Exception as e:
            print(f"命令执行失败: {command}, 错误: {e}")
            return False, "", str(e)

    def download_file(self, url, filename):
        """下载文件"""
        filepath = self.temp_dir / filename
        try:
            print(f"正在下载 {filename}...")
            urllib.request.urlretrieve(url, filepath)
            print(f"下载完成: {filepath}")
            return filepath
        except Exception as e:
            print(f"下载失败: {e}")
            return None

    def check_python(self):
        """检查Python版本"""
        print("检查Python环境...")
        try:
            version = sys.version_info
            if version.major == 3 and version.minor >= 11:
                print(f"✓ Python {version.major}.{version.minor}.{version.micro} 已安装")
                return True
            else:
                print(f"✗ Python版本过低: {version.major}.{version.minor}.{version.micro}")
                return self.install_python()
        except Exception as e:
            print(f"✗ Python检查失败: {e}")
            return self.install_python()

    def install_python(self):
        """安装Python 3.11"""
        print("正在安装Python 3.11...")
        if self.is_windows:
            # Windows安装Python
            url = "https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe"
            installer = self.download_file(url, "python-3.11.7-amd64.exe")
            if installer:
                success, _, _ = self.run_command(f'"{installer}" /quiet InstallAllUsers=1 PrependPath=1')
                return success
        elif self.is_linux:
            # Linux安装Python
            success, _, _ = self.run_command("sudo apt update")
            if success:
                success, _, _ = self.run_command("sudo apt install -y python3.11 python3.11-pip python3.11-venv")
                return success
        return False

    def check_java(self):
        """检查Java环境"""
        print("检查Java环境...")

        # 首先检查环境变量中的java
        success, output, _ = self.run_command("java -version")
        if success and "17" in output:
            print("✓ Java 17 已安装并在PATH中")
            return True

        # 检查特定安装路径
        if self.is_windows:
            java_path = r"C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe"
            if Path(java_path).exists():
                success, output, _ = self.run_command(f'"{java_path}" -version')
                if success and "17" in output:
                    print(f"✓ Java 17 已安装在: {java_path}")
                    print("⚠ 需要配置JAVA_HOME环境变量")
                    return True

        print("✗ Java 17 未安装或版本不正确")
        return self.install_java()

    def install_java(self):
        """安装Java 17"""
        print("正在安装Java 17...")
        if self.is_windows:
            print("Windows Java 17 安装指南:")
            print("1. 访问 https://adoptium.net/")
            print("2. 选择 OpenJDK 17 (LTS)")
            print("3. 选择 Windows x64 版本")
            print("4. 下载并运行安装程序")
            print("5. 安装完成后重新运行此脚本")
            print("\n或者使用包管理器安装:")
            print("  winget install EclipseAdoptium.Temurin.17.JDK")
            print("  或")
            print("  choco install temurin17")
            return False
        elif self.is_linux:
            # Linux安装Java
            print("正在安装OpenJDK 17...")
            success, _, _ = self.run_command("sudo apt update")
            if success:
                success, _, _ = self.run_command("sudo apt install -y openjdk-17-jdk")
                if success:
                    print("✓ Java 17 安装成功")
                    return True
                else:
                    print("✗ Java 17 安装失败")
                    return False
            else:
                print("✗ 系统更新失败")
                return False
        return False

    def check_nodejs(self):
        """检查Node.js环境"""
        print("检查Node.js环境...")
        success, output, _ = self.run_command("node --version")
        if success and "v18" in output or "v20" in output:
            print("✓ Node.js 已安装")
            return True
        else:
            print("✗ Node.js 未安装或版本不正确")
            return self.install_nodejs()

    def install_nodejs(self):
        """安装Node.js"""
        print("正在安装Node.js...")
        if self.is_windows:
            # Windows安装Node.js
            url = "https://nodejs.org/dist/v18.19.0/node-v18.19.0-x64.msi"
            installer = self.download_file(url, "node-v18.19.0-x64.msi")
            if installer:
                success, _, _ = self.run_command(f'msiexec /i "{installer}" /quiet')
                return success
        elif self.is_linux:
            # Linux安装Node.js
            commands = [
                "curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -",
                "sudo apt-get install -y nodejs"
            ]
            for cmd in commands:
                success, _, _ = self.run_command(cmd)
                if not success:
                    return False
            return True
        return False

    def check_mysql(self):
        """检查MySQL服务"""
        print("检查MySQL服务...")

        # 首先检查MySQL是否安装
        success, output, _ = self.run_command('mysql --version')
        if not success:
            print("✗ MySQL 未安装")
            return self.install_mysql()
        else:
            print(f"✓ MySQL 已安装: {output.split()[2] if len(output.split()) > 2 else 'Unknown version'}")

        # 检查服务状态
        if self.is_windows:
            # Windows下检查MySQL服务
            success, _, _ = self.run_command('sc query mysql80')
            if not success:
                success, _, _ = self.run_command('sc query mysql')
        else:
            success, _, _ = self.run_command('systemctl is-active mysql')
            if not success:
                success, _, _ = self.run_command('systemctl is-active mysqld')

        if success:
            print("✓ MySQL 服务运行中")
            return self.check_mysql_database()
        else:
            print("✗ MySQL 服务未运行，尝试启动...")
            return self.start_mysql_service()

    def start_mysql_service(self):
        """启动MySQL服务"""
        if self.is_windows:
            success, _, _ = self.run_command('net start mysql80')
            if not success:
                success, _, _ = self.run_command('net start mysql')
        else:
            success, _, _ = self.run_command('sudo systemctl start mysql')
            if not success:
                success, _, _ = self.run_command('sudo systemctl start mysqld')

        if success:
            print("✓ MySQL 服务启动成功")
            return self.check_mysql_database()
        else:
            print("✗ MySQL 服务启动失败")
            return self.install_mysql()

    def check_mysql_database(self):
        """检查数据库是否存在"""
        print("检查数据库 testupbm...")
        success, output, _ = self.run_command(f'{self.mysql_path} -u root -p123456 -e "SHOW DATABASES LIKE \'testupbm\';"')
        if success and 'testupbm' in output:
            print("✓ 数据库 testupbm 已存在")
            return True
        else:
            print("创建数据库 testupbm...")
            success, _, _ = self.run_command(f'{self.mysql_path} -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS testupbm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"')
            if success:
                print("✓ 数据库创建成功")
                return True
            else:
                print("✗ 数据库创建失败，请检查MySQL用户名和密码")
                return False

    def install_mysql(self):
        """安装MySQL"""
        print("正在安装MySQL...")
        if self.is_windows:
            print("Windows MySQL 自动安装...")
            # 下载MySQL安装包
            url = "https://dev.mysql.com/get/Downloads/MySQLInstaller/mysql-installer-community-8.0.35.0.msi"
            installer = self.download_file(url, "mysql-installer.msi")
            if installer:
                print("请手动运行安装程序并设置root密码为: 123456")
                print(f"安装程序位置: {installer}")
                input("安装完成后按回车继续...")
                return self.check_mysql_database()
            return False
        elif self.is_linux:
            print("Linux MySQL 自动安装...")
            commands = [
                "sudo apt update",
                "sudo DEBIAN_FRONTEND=noninteractive apt install -y mysql-server",
                "sudo systemctl start mysql",
                "sudo systemctl enable mysql"
            ]
            for cmd in commands:
                success, _, _ = self.run_command(cmd)
                if not success:
                    print(f"命令执行失败: {cmd}")
                    return False

            # 设置root密码
            print("设置MySQL root密码...")
            set_password_cmd = 'sudo mysql -e "ALTER USER \'root\'@\'localhost\' IDENTIFIED WITH mysql_native_password BY \'123456\'; FLUSH PRIVILEGES;"'
            success, _, _ = self.run_command(set_password_cmd)
            if success:
                return self.check_mysql_database()
            else:
                print("密码设置失败，尝试无密码创建数据库...")
                success, _, _ = self.run_command('sudo mysql -e "CREATE DATABASE IF NOT EXISTS testupbm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"')
                return success
        return False

    def get_redis_path(self):
        """获取Redis可执行文件路径"""
        if self.is_windows:
            # 常见的Redis安装路径
            possible_paths = [
                r'"C:\Program Files\Redis\redis-cli.exe"',
                r'"C:\Program Files (x86)\Redis\redis-cli.exe"',
                r'"C:\Redis\redis-cli.exe"',
                'redis-cli'  # 如果在PATH中
            ]
        else:
            possible_paths = ['redis-cli']

        for path in possible_paths:
            success, _, _ = self.run_command(f'{path} --version')
            if success:
                return path
        return 'redis-cli'  # 默认值

    def check_redis(self):
        """检查Redis服务"""
        print("检查Redis服务...")

        redis_path = self.get_redis_path()
        success, output, _ = self.run_command(f'{redis_path} ping')

        if success and 'PONG' in output:
            print("✓ Redis 服务运行中")
            return True
        else:
            print("✗ Redis 服务未运行")
            # 尝试启动Redis服务
            return self.start_redis_service()

    def start_redis_service(self):
        """启动Redis服务"""
        print("尝试启动Redis服务...")

        if self.is_windows:
            # 尝试启动Windows Redis服务
            success, _, _ = self.run_command('net start redis')
            if not success:
                # 尝试直接运行Redis服务器
                redis_server_path = r'"C:\Program Files\Redis\redis-server.exe"'
                success, _, _ = self.run_command(f'start /B {redis_server_path}')
        else:
            success, _, _ = self.run_command('sudo systemctl start redis-server')
            if not success:
                success, _, _ = self.run_command('sudo systemctl start redis')

        if success:
            print("✓ Redis 服务启动成功")
            # 等待服务启动
            import time
            time.sleep(2)
            # 再次检查
            redis_path = self.get_redis_path()
            success, output, _ = self.run_command(f'{redis_path} ping')
            return success and 'PONG' in output
        else:
            print("✗ Redis 服务启动失败")
            return self.install_redis()

    def install_redis(self):
        """安装Redis"""
        print("正在安装Redis...")
        if self.is_windows:
            print("请手动下载并安装Redis: https://github.com/microsoftarchive/redis/releases")
            return False
        elif self.is_linux:
            commands = [
                "sudo apt update",
                "sudo apt install -y redis-server",
                "sudo systemctl start redis-server",
                "sudo systemctl enable redis-server"
            ]
            for cmd in commands:
                success, _, _ = self.run_command(cmd)
                if not success:
                    return False
            return True
        return False

    def check_python_dependencies(self):
        """检查Python依赖是否已安装"""
        print("检查Python依赖...")

        # 检查当前环境中的关键依赖
        required_packages = [
            'flask', 'ultralytics', 'opencv-python', 'numpy', 'Pillow', 'torch', 'torchvision'
        ]

        missing_packages = []
        for package in required_packages:
            success, _, _ = self.run_command(f'python -c "import {package.replace("-", "_")}"')
            if not success:
                missing_packages.append(package)

        if missing_packages:
            print(f"✗ 缺少依赖包: {', '.join(missing_packages)}")
            return False
        else:
            print("✓ 所有Python依赖已安装")
            return True

    def setup_python_environment(self):
        """设置Python虚拟环境和依赖"""
        print("设置Python环境...")

        # 检查pules项目
        pules_dir = self.project_root / 'pules'
        if not pules_dir.exists():
            print("✗ pules目录不存在")
            return False

        # 首先检查当前环境是否已有依赖
        if self.check_python_dependencies():
            print("✓ 当前Python环境已满足要求")
            return True

        # 检查是否在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

        if in_venv:
            print("✓ 检测到虚拟环境，尝试安装缺失的依赖...")
            requirements_file = pules_dir / 'requirements.txt'
            if requirements_file.exists():
                print("安装Python依赖...")
                try:
                    success, output, error = self.run_command(f'pip install -r "{requirements_file}"', timeout=600)
                    if success:
                        print("✓ 依赖安装成功")
                        return True
                    else:
                        print(f"✗ 依赖安装失败: {error}")
                        return False
                except Exception as e:
                    print(f"✗ 依赖安装过程中断: {e}")
                    return False
            else:
                print("✗ requirements.txt 文件不存在")
                return False
        else:
            # 创建虚拟环境
            venv_dir = pules_dir / 'venv'
            if not venv_dir.exists():
                print("创建Python虚拟环境...")
                success, _, _ = self.run_command(f'python -m venv "{venv_dir}"')
                if not success:
                    print("✗ 虚拟环境创建失败")
                    return False

            # 激活虚拟环境并安装依赖
            if self.is_windows:
                pip_cmd = f'"{venv_dir}/Scripts/pip.exe"'
            else:
                pip_cmd = f'"{venv_dir}/bin/pip"'

            requirements_file = pules_dir / 'requirements.txt'
            if requirements_file.exists():
                print("在虚拟环境中安装Python依赖...")
                try:
                    success, output, error = self.run_command(f'{pip_cmd} install -r "{requirements_file}"', timeout=600)
                    if success:
                        print("✓ 虚拟环境依赖安装成功")
                        return True
                    else:
                        print(f"✗ 虚拟环境依赖安装失败: {error}")
                        return False
                except Exception as e:
                    print(f"✗ 虚拟环境依赖安装过程中断: {e}")
                    return False
            else:
                print("✗ requirements.txt 文件不存在")
                return False

    def check_gradle(self):
        """检查Gradle环境"""
        print("检查Gradle环境...")

        # 检查本地Gradle安装
        if self.is_windows:
            gradle_home = Path("D:/gradle-8.3")
            if gradle_home.exists():
                print(f"✓ 本地Gradle安装已找到: {gradle_home}")
            else:
                print("⚠ 本地Gradle安装路径不存在: D:/gradle-8.3")

        # 检查upbm项目
        upbm_dir = self.project_root / 'upbm'
        if not upbm_dir.exists():
            print("✗ upbm目录不存在")
            return False

        # 检查gradlew文件
        if self.is_windows:
            gradlew = upbm_dir / 'gradlew.bat'
        else:
            gradlew = upbm_dir / 'gradlew'

        if gradlew.exists():
            print("✓ Gradle Wrapper 已存在")

            # 检查Java环境（Gradle需要Java）
            java_path = r"C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe"
            if Path(java_path).exists():
                print("✓ Java 17 已找到，Gradle可以使用")
            else:
                java_success, java_output, _ = self.run_command("java -version")
                if not java_success:
                    print("✗ Gradle 需要Java环境，但Java未安装")
                    return False

            # 测试gradle（使用较短的超时时间）
            original_dir = os.getcwd()
            try:
                os.chdir(upbm_dir)
                print("测试Gradle配置...")
                success, output, error = self.run_command(f'"{gradlew}" --version', timeout=60)
                if success:
                    print("✓ Gradle 工作正常")
                    return True
                else:
                    print("⚠️  Gradle 测试失败，但Wrapper存在")
                    print(f"   错误信息: {error}")
                    # 即使测试失败，如果Wrapper存在也认为基本可用
                    return True
            finally:
                os.chdir(original_dir)
        else:
            print("✗ Gradle Wrapper 不存在")
            return False

    def setup_nodejs_environment(self):
        """设置Node.js环境和依赖"""
        print("设置Node.js环境...")

        # 检查uloi项目
        uloi_dir = self.project_root / 'uloi'
        if not uloi_dir.exists():
            print("✗ uloi目录不存在")
            return False

        # 安装依赖
        original_dir = os.getcwd()
        try:
            os.chdir(uloi_dir)
            print("安装Node.js依赖...")

            # 首先安装yarn
            success, _, _ = self.run_command('npm install -g yarn')

            # 尝试使用yarn，如果失败则使用npm
            success, _, _ = self.run_command('yarn --version')
            if success:
                print("使用 yarn 安装依赖...")
                success, output, error = self.run_command('yarn install')
                if not success:
                    print(f"yarn 安装失败: {error}")
                    print("尝试使用 npm...")
                    success, _, _ = self.run_command('npm install')
            else:
                print("使用 npm 安装依赖...")
                success, _, _ = self.run_command('npm install')

            return success
        finally:
            os.chdir(original_dir)

    def setup_gradle_environment(self):
        """设置Gradle环境"""
        print("设置Gradle环境...")

        upbm_dir = self.project_root / 'upbm'
        if not upbm_dir.exists():
            print("✗ upbm目录不存在")
            return False

        original_dir = os.getcwd()
        try:
            os.chdir(upbm_dir)

            # 检查并下载依赖
            if self.is_windows:
                gradlew = './gradlew.bat'
            else:
                gradlew = './gradlew'
                # 确保gradlew有执行权限
                self.run_command('chmod +x gradlew')

            print("下载Gradle依赖...")
            success, output, error = self.run_command(f'{gradlew} dependencies')
            if success:
                print("✓ Gradle依赖下载完成")
                return True
            else:
                print(f"✗ Gradle依赖下载失败: {error}")
                return False
        finally:
            os.chdir(original_dir)

    def create_directories(self):
        """创建必要的目录"""
        print("创建必要的目录...")
        
        if self.is_windows:
            dirs = [
                "D:/code/kotlin/upbmTest/img/data",
                "D:/code/kotlin/upbmTest/img/process", 
                "D:/code/kotlin/upbmTest/img/result"
            ]
        else:
            dirs = [
                "/opt/upbm/img/data",
                "/opt/upbm/img/process",
                "/opt/upbm/img/result"
            ]
            
        for dir_path in dirs:
            try:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                print(f"✓ 创建目录: {dir_path}")
            except Exception as e:
                print(f"✗ 创建目录失败: {dir_path}, 错误: {e}")
                return False
        return True

    def cleanup(self):
        """清理临时文件"""
        try:
            shutil.rmtree(self.temp_dir)
            print("✓ 清理临时文件完成")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    def generate_startup_scripts(self):
        """生成启动脚本"""
        print("生成启动脚本...")

        if self.is_windows:
            # Windows批处理脚本
            startup_script = """@echo off
echo 启动电力线路设备缺陷识别系统
echo ================================

echo 启动MySQL服务...
net start mysql80 2>nul || net start mysql 2>nul

echo 启动Redis服务...
net start redis 2>nul

echo 等待服务启动...
timeout /t 3 /nobreak >nul

echo 启动识别服务 (pules)...
cd /d "%~dp0pules"
start "识别服务" cmd /k "venv\\Scripts\\activate && python main.py"

echo 等待识别服务启动...
timeout /t 5 /nobreak >nul

echo 启动后端服务 (upbm)...
cd /d "%~dp0upbm"
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set GRADLE_HOME=D:\gradle-8.3
set PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH%
start "后端服务" cmd /k "gradle bootRun"

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

echo 启动前端服务 (uloi)...
cd /d "%~dp0uloi"
start "前端服务" cmd /k "npm run dev"

echo 所有服务启动完成!
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:18084
echo 识别服务: http://localhost:5000
pause
"""
            script_path = self.project_root / "启动系统.bat"
        else:
            # Linux shell脚本
            startup_script = """#!/bin/bash
echo "启动电力线路设备缺陷识别系统"
echo "================================"

echo "启动MySQL服务..."
sudo systemctl start mysql || sudo systemctl start mysqld

echo "启动Redis服务..."
sudo systemctl start redis-server || sudo systemctl start redis

echo "等待服务启动..."
sleep 3

echo "启动识别服务 (pules)..."
cd "$(dirname "$0")/pules"
source venv/bin/activate
python main.py &
PULES_PID=$!

echo "等待识别服务启动..."
sleep 5

echo "启动后端服务 (upbm)..."
cd "$(dirname "$0")/upbm"
./gradlew bootRun &
UPBM_PID=$!

echo "等待后端服务启动..."
sleep 10

echo "启动前端服务 (uloi)..."
cd "$(dirname "$0")/uloi"
npm run dev &
ULOI_PID=$!

echo "所有服务启动完成!"
echo "前端地址: http://localhost:5173"
echo "后端地址: http://localhost:18084"
echo "识别服务: http://localhost:5000"

echo "按Ctrl+C停止所有服务"
trap "kill $PULES_PID $UPBM_PID $ULOI_PID 2>/dev/null; exit" INT
wait
"""
            script_path = self.project_root / "启动系统.sh"

        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(startup_script)

            if not self.is_windows:
                # 给shell脚本执行权限
                self.run_command(f'chmod +x "{script_path}"')

            print(f"✓ 启动脚本已生成: {script_path}")
            return True
        except Exception as e:
            print(f"✗ 启动脚本生成失败: {e}")
            return False

    def run_checks(self):
        """运行所有检查和安装"""
        print("=" * 60)
        print("电力线路设备缺陷识别系统 - 环境检查和自动配置")
        print("=" * 60)

        # 基础软件检查
        basic_checks = [
            ("Python 3.11+", self.check_python),
            ("Java 17", self.check_java),
            ("Node.js 18+", self.check_nodejs),
        ]

        # 服务检查
        service_checks = [
            ("MySQL", self.check_mysql),
            ("Redis", self.check_redis),
        ]

        # 项目环境检查
        project_checks = [
            ("Gradle", self.check_gradle),
        ]

        failed_checks = []

        # 执行基础软件检查
        print("\n🔍 基础软件环境检查")
        print("-" * 30)
        for name, check_func in basic_checks:
            print(f"\n检查 {name}...")
            if not check_func():
                failed_checks.append(name)

        # 执行服务检查
        print("\n🔍 数据库和缓存服务检查")
        print("-" * 30)
        for name, check_func in service_checks:
            print(f"\n检查 {name}...")
            if not check_func():
                failed_checks.append(name)

        # 执行项目环境检查
        print("\n🔍 项目环境检查")
        print("-" * 30)
        for name, check_func in project_checks:
            print(f"\n检查 {name}...")
            if not check_func():
                failed_checks.append(name)

        # 设置项目环境
        print("\n🔧 项目环境配置")
        print("-" * 30)

        print("\n配置Python环境...")
        if not self.setup_python_environment():
            failed_checks.append("Python环境设置")

        print("\n配置Node.js环境...")
        if not self.setup_nodejs_environment():
            failed_checks.append("Node.js环境设置")

        print("\n配置Gradle环境...")
        if not self.setup_gradle_environment():
            failed_checks.append("Gradle环境设置")

        print("\n创建必要目录...")
        if not self.create_directories():
            failed_checks.append("目录创建")

        print("\n生成启动脚本...")
        if not self.generate_startup_scripts():
            failed_checks.append("启动脚本生成")

        # 清理临时文件
        self.cleanup()

        # 输出结果
        print("\n" + "=" * 60)
        print("环境检查和配置完成")
        print("=" * 60)

        if failed_checks:
            print("❌ 以下组件安装/配置失败:")
            for item in failed_checks:
                print(f"   - {item}")
            print("\n⚠️  请手动处理失败的组件后重新运行此脚本")
            print("或者查看上面的错误信息进行排查")
            return False
        else:
            print("✅ 所有环境检查和配置成功完成!")
            print("\n🚀 下一步操作:")
            if self.is_windows:
                print("   双击运行 '启动系统.bat' 启动所有服务")
            else:
                print("   运行 './启动系统.sh' 启动所有服务")
            print("\n🌐 服务地址:")
            print("   前端界面: http://localhost:5173")
            print("   后端API:  http://localhost:18084")
            print("   识别服务: http://localhost:5000")
            print("\n📖 更多信息请查看 '环境配置手册.md'")
            return True

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: python 环境检查和自动配置.py")
        print("此脚本将检查并自动安装项目所需的所有环境依赖")
        sys.exit(0)
    
    # 检查是否以管理员权限运行
    if platform.system() == "Windows":
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("请以管理员权限运行此脚本")
            sys.exit(1)
    elif platform.system() == "Linux":
        if os.geteuid() != 0:
            print("请使用sudo运行此脚本")
            sys.exit(1)
    
    checker = EnvironmentChecker()
    success = checker.run_checks()
    sys.exit(0 if success else 1)
