#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版环境检查脚本
针对已有环境进行快速验证和配置
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(command, timeout=30):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def check_python_environment():
    """检查Python环境"""
    print("🐍 检查Python环境...")
    
    # 检查Python版本
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    else:
        print(f"⚠️  Python版本较低: {version.major}.{version.minor}.{version.micro}")
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    if in_venv:
        print("✅ 当前在虚拟环境中")
    else:
        print("⚠️  当前不在虚拟环境中")
    
    # 检查关键依赖
    required_packages = {
        'flask': 'Flask',
        'ultralytics': 'ultralytics', 
        'cv2': 'opencv-python',
        'numpy': 'numpy',
        'PIL': 'Pillow',
        'torch': 'torch',
        'torchvision': 'torchvision'
    }
    
    missing_packages = []
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")
            missing_packages.append(package_name)
    
    return len(missing_packages) == 0, missing_packages

def check_java_environment():
    """检查Java环境"""
    print("\n☕ 检查Java环境...")

    # 首先检查环境变量中的java
    success, output, _ = run_command("java -version")
    if success and "17" in output:
        print("✅ Java 17 已安装并在PATH中")
        return True
    elif success:
        print(f"⚠️  Java已安装但版本不是17: {output.split()[2] if len(output.split()) > 2 else 'Unknown'}")

    # 检查特定安装路径
    java_path = r"C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe"
    if Path(java_path).exists():
        success, output, _ = run_command(f'"{java_path}" -version')
        if success and "17" in output:
            print(f"✅ Java 17 已安装在: {java_path}")
            print("⚠️  需要配置JAVA_HOME环境变量")
            return True

    print("❌ Java 17 未安装或版本不正确")
    return False

def check_nodejs_environment():
    """检查Node.js环境"""
    print("\n🟢 检查Node.js环境...")
    
    success, output, _ = run_command("node --version")
    if success:
        version = output.replace('v', '')
        major_version = int(version.split('.')[0])
        if major_version >= 16:
            print(f"✅ Node.js {version}")
            return True
        else:
            print(f"⚠️  Node.js版本较低: {version}")
            return False
    else:
        print("❌ Node.js 未安装")
        return False

def check_databases():
    """检查数据库"""
    print("\n🗄️  检查数据库...")
    
    # 检查MySQL
    mysql_paths = [
        r'"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe"',
        'mysql'
    ]
    
    mysql_ok = False
    for mysql_path in mysql_paths:
        success, output, _ = run_command(f'{mysql_path} -u root -p123456 -e "SELECT 1;"')
        if success:
            print("✅ MySQL 连接正常")
            mysql_ok = True
            break
    
    if not mysql_ok:
        print("❌ MySQL 连接失败")
    
    # 检查Redis
    redis_paths = [
        r'"C:\Program Files\Redis\redis-cli.exe"',
        'redis-cli'
    ]
    
    redis_ok = False
    for redis_path in redis_paths:
        success, output, _ = run_command(f'{redis_path} ping')
        if success and 'PONG' in output:
            print("✅ Redis 连接正常")
            redis_ok = True
            break
    
    if not redis_ok:
        print("❌ Redis 连接失败")
    
    return mysql_ok and redis_ok

def check_project_structure():
    """检查项目结构"""
    print("\n📁 检查项目结构...")
    
    project_root = Path(__file__).parent
    required_dirs = ['pules', 'uloi', 'upbm']
    
    all_exist = True
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/")
            all_exist = False
    
    return all_exist

def install_nodejs_dependencies():
    """安装Node.js依赖"""
    print("\n📦 安装Node.js依赖...")
    
    project_root = Path(__file__).parent
    uloi_dir = project_root / 'uloi'
    
    if not uloi_dir.exists():
        print("❌ uloi目录不存在")
        return False
    
    original_dir = os.getcwd()
    try:
        os.chdir(uloi_dir)
        
        # 检查package.json
        if not (uloi_dir / 'package.json').exists():
            print("❌ package.json 不存在")
            return False
        
        # 检查node_modules
        if (uloi_dir / 'node_modules').exists():
            print("✅ Node.js依赖已安装")
            return True
        
        print("正在安装Node.js依赖...")
        success, output, error = run_command('npm install', timeout=300)
        
        if success:
            print("✅ Node.js依赖安装成功")
            return True
        else:
            print(f"❌ Node.js依赖安装失败: {error}")
            return False
            
    finally:
        os.chdir(original_dir)

def generate_startup_scripts():
    """生成启动脚本"""
    print("\n🚀 生成启动脚本...")
    
    project_root = Path(__file__).parent
    
    # Windows启动脚本
    startup_script = """@echo off
echo 启动电力线路设备缺陷识别系统
echo ================================

echo 启动MySQL服务...
net start mysql80 2>nul || net start mysql 2>nul

echo 启动Redis服务...
net start redis 2>nul

echo 等待服务启动...
timeout /t 3 /nobreak >nul

echo 启动识别服务 (pules)...
cd /d "%~dp0pules"
start "识别服务" cmd /k "python main.py"

echo 等待识别服务启动...
timeout /t 5 /nobreak >nul

echo 启动后端服务 (upbm)...
cd /d "%~dp0upbm"
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set GRADLE_HOME=D:\gradle-8.3
set PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH%
start "后端服务" cmd /k "gradlew.bat bootRun"

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

echo 启动前端服务 (uloi)...
cd /d "%~dp0uloi"
start "前端服务" cmd /k "npm run dev"

echo 所有服务启动完成!
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:18084
echo 识别服务: http://localhost:5000
pause
"""
    
    script_path = project_root / "启动系统.bat"
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        print(f"✅ 启动脚本已生成: {script_path}")
        return True
    except Exception as e:
        print(f"❌ 启动脚本生成失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("电力线路设备缺陷识别系统 - 简化环境检查")
    print("="*60)
    
    results = []
    
    # 检查Python环境
    python_ok, missing_packages = check_python_environment()
    results.append(("Python环境", python_ok))
    
    # 检查Java环境
    java_ok = check_java_environment()
    results.append(("Java环境", java_ok))
    
    # 检查Node.js环境
    nodejs_ok = check_nodejs_environment()
    results.append(("Node.js环境", nodejs_ok))
    
    # 检查数据库
    db_ok = check_databases()
    results.append(("数据库", db_ok))
    
    # 检查项目结构
    project_ok = check_project_structure()
    results.append(("项目结构", project_ok))
    
    # 安装Node.js依赖
    if nodejs_ok and project_ok:
        npm_ok = install_nodejs_dependencies()
        results.append(("Node.js依赖", npm_ok))
    
    # 生成启动脚本
    script_ok = generate_startup_scripts()
    results.append(("启动脚本", script_ok))
    
    # 输出总结
    print("\n" + "="*60)
    print("环境检查总结")
    print("="*60)
    
    for name, status in results:
        icon = "✅" if status else "❌"
        print(f"{icon} {name}")
    
    # 给出建议
    print("\n💡 建议:")
    
    if not python_ok and missing_packages:
        print(f"   安装Python依赖: pip install {' '.join(missing_packages)}")
    
    if not java_ok:
        print("   安装Java 17: 访问 https://adoptium.net/")
    
    if python_ok and db_ok and project_ok:
        print("   ✅ 核心环境已就绪，可以启动系统")
        print("   运行: 启动系统.bat")
    
    print("\n🌐 服务地址:")
    print("   前端: http://localhost:5173")
    print("   后端: http://localhost:18084") 
    print("   识别: http://localhost:5000")

if __name__ == "__main__":
    main()
