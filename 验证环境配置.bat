@echo off
echo 验证Java和Gradle环境配置
echo ========================

REM 临时设置环境变量
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set GRADLE_HOME=D:\gradle-8.3
set PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH%

echo 1. 检查Java安装...
echo JAVA_HOME: %JAVA_HOME%
if exist "%JAVA_HOME%\bin\java.exe" (
    echo ✓ Java可执行文件存在
    "%JAVA_HOME%\bin\java.exe" -version
) else (
    echo ✗ Java可执行文件不存在
)

echo.
echo 2. 检查Gradle安装...
echo GRADLE_HOME: %GRADLE_HOME%
if exist "%GRADLE_HOME%\bin\gradle.bat" (
    echo ✓ Gradle可执行文件存在
    "%GRADLE_HOME%\bin\gradle.bat" --version
) else (
    echo ✗ Gradle可执行文件不存在
)

echo.
echo 3. 测试Spring Boot项目...
cd /d "%~dp0upbm"
if exist "build.gradle.kts" (
    echo ✓ 找到Spring Boot项目
    echo 测试Gradle构建...
    "%GRADLE_HOME%\bin\gradle.bat" tasks --all | findstr "bootRun"
    if %errorlevel% equ 0 (
        echo ✓ bootRun任务可用
    ) else (
        echo ⚠ bootRun任务检查失败
    )
) else (
    echo ✗ 未找到Spring Boot项目
)

echo.
echo 验证完成！
pause
