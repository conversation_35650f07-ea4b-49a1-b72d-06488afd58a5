@echo off
echo 启动电力线路设备缺陷识别系统 (使用本地Gradle)
echo ================================================

REM 设置环境变量
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set GRADLE_HOME=D:\gradle-8.3
set PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH%

echo 环境变量设置:
echo JAVA_HOME=%JAVA_HOME%
echo GRADLE_HOME=%GRADLE_HOME%
echo.

echo 启动MySQL服务...
net start mysql80 2>nul || net start mysql 2>nul

echo 启动Redis服务...
net start redis 2>nul

echo 等待服务启动...
timeout /t 3 /nobreak >nul

echo 启动识别服务 (pules)...
cd /d "%~dp0pules"
start "识别服务" cmd /k "python main.py"

echo 等待识别服务启动...
timeout /t 5 /nobreak >nul

echo 启动后端服务 (upbm) - 使用本地Gradle...
cd /d "%~dp0upbm"
echo 当前目录: %CD%
echo 使用Gradle命令: gradle bootRun
start "后端服务" cmd /k "set JAVA_HOME=%JAVA_HOME% && set GRADLE_HOME=%GRADLE_HOME% && set PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH% && gradle bootRun"

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

echo 启动前端服务 (uloi)...
cd /d "%~dp0uloi"
start "前端服务" cmd /k "npm run dev"

echo 所有服务启动完成!
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:18084
echo 识别服务: http://localhost:5000
echo.
echo 注意: 使用本地Gradle安装 (D:\gradle-8.3)
pause
